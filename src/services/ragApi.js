import { apiRequest } from './api';

/**
 * RAG 智能助手 API 服务
 */
class RAGApiService {
  constructor() {
    this.baseUrl = '/api/rag';
  }

  /**
   * 发送问题并获取回答
   * @param {string} question - 用户问题
   * @returns {Promise<Object>} 问答结果
   */
  async askQuestion(question) {
    try {
      const response = await apiRequest(`${this.baseUrl}/ask`, {
        method: 'POST',
        body: JSON.stringify({ question })
      });

      return response.data;
    } catch (error) {
      console.error('❌ RAG 问答请求失败:', error);
      throw error;
    }
  }

  /**
   * 流式问答
   * @param {string} question - 用户问题
   * @param {Function} onMessage - 消息回调函数
   * @param {Function} onError - 错误回调函数
   * @param {Function} onComplete - 完成回调函数
   * @returns {Function} 取消函数
   */
  askQuestionStream(question, onMessage, onError, onComplete) {
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
    const url = `${API_BASE_URL}${this.baseUrl}/ask-stream`;
    
    // 获取认证令牌
    const token = localStorage.getItem('authToken');
    
    const eventSource = new EventSource(url, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    });

    // 发送问题
    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify({ question })
    }).catch(error => {
      console.error('❌ 发送流式问答请求失败:', error);
      onError?.(error);
    });

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage?.(data);
        
        if (data.type === 'complete') {
          eventSource.close();
          onComplete?.();
        } else if (data.type === 'error') {
          eventSource.close();
          onError?.(new Error(data.message));
        }
      } catch (error) {
        console.error('❌ 解析流式响应失败:', error);
        onError?.(error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('❌ 流式连接错误:', error);
      eventSource.close();
      onError?.(error);
    };

    // 返回取消函数
    return () => {
      eventSource.close();
    };
  }

  /**
   * 获取 RAG 服务状态
   * @returns {Promise<Object>} 服务状态
   */
  async getStatus() {
    try {
      const response = await apiRequest(`${this.baseUrl}/status`);
      return response.status;
    } catch (error) {
      console.error('❌ 获取 RAG 状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取建议问题
   * @returns {Promise<Array>} 建议问题列表
   */
  async getSuggestions() {
    try {
      const response = await apiRequest(`${this.baseUrl}/suggestions`);
      return response.suggestions;
    } catch (error) {
      console.error('❌ 获取建议问题失败:', error);
      throw error;
    }
  }

  /**
   * 构建知识库
   * @returns {Promise<Object>} 构建结果
   */
  async buildKnowledgeBase() {
    try {
      const response = await apiRequest(`${this.baseUrl}/build-knowledge-base`, {
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('❌ 构建知识库失败:', error);
      throw error;
    }
  }

  /**
   * 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    try {
      const response = await apiRequest(`${this.baseUrl}/health`);
      return response;
    } catch (error) {
      console.error('❌ RAG 健康检查失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const ragApiService = new RAGApiService();

export default ragApiService;
