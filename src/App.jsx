import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import MainPage from './pages/MainPage';
import ParkDetailPage from './pages/ParkDetailPage';
import WelcomePage from './pages/WelcomePage';
import LoginPage from './pages/LoginPage';
import ProfilePage from './pages/ProfilePage';
import AIAnalyzerPage from './pages/AIAnalyzerPage';
import AIProjectPage from './pages/AIProjectPage';
import SiteSelectionPage from './pages/SiteSelectionPage';
import TestPage from './pages/TestPage';
import AIAssistant from './components/common/AIAssistant';
import './styles/App.css'; // 添加全局样式
import './styles/park-popup.css'; // 工业园区弹出窗口样式

function App() {
  return (
    <div className="app-container">
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<WelcomePage />} />
          <Route path="/test" element={<TestPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/profile" element={<ProfilePage />} />
          <Route path="/main" element={<MainPage />} />
          <Route path="/park/:id" element={<ParkDetailPage />} />
          <Route path="/ai-analyzer" element={<AIAnalyzerPage />} />
          <Route path="/ai-project/:id" element={<AIProjectPage />} />
          <Route path="/site-selection" element={<SiteSelectionPage />} />
        </Routes>

        {/* 全局智能助手 - 在所有页面都可用 */}
        <AIAssistant />
      </BrowserRouter>
    </div>
  );
}

export default App;
