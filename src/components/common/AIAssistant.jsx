import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ragApiService from '../../services/ragApi';
import './AIAssistant.css';

/**
 * AI 智能助手组件
 * 提供浮动式聊天界面，支持流式问答和液体玻璃设计
 */
const AIAssistant = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [status, setStatus] = useState(null);
  
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const cancelStreamRef = useRef(null);

  // 初始化组件
  useEffect(() => {
    loadSuggestions();
    checkStatus();
  }, []);

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 聚焦输入框
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  /**
   * 加载建议问题
   */
  const loadSuggestions = async () => {
    try {
      const suggestionsData = await ragApiService.getSuggestions();
      setSuggestions(suggestionsData);
    } catch (error) {
      console.error('加载建议问题失败:', error);
    }
  };

  /**
   * 检查服务状态
   */
  const checkStatus = async () => {
    try {
      const statusData = await ragApiService.getStatus();
      setStatus(statusData);
    } catch (error) {
      console.error('检查服务状态失败:', error);
      setStatus({ initialized: false, error: error.message });
    }
  };

  /**
   * 滚动到底部
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * 切换助手面板
   */
  const toggleAssistant = () => {
    setIsOpen(!isOpen);
    if (!isOpen && messages.length === 0) {
      // 首次打开时显示欢迎消息
      setMessages([{
        id: Date.now(),
        type: 'assistant',
        content: '👋 您好！我是 Industrial Geo Dev 的智能助手。我可以帮助您了解系统功能、解答技术问题、提供使用指导。请问有什么可以帮助您的吗？',
        timestamp: new Date()
      }]);
    }
  };

  /**
   * 发送消息
   */
  const sendMessage = async (question = inputValue.trim()) => {
    if (!question || isLoading) return;

    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: question,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setShowSuggestions(false);

    // 添加助手消息占位符
    const assistantMessageId = Date.now() + 1;
    const assistantMessage = {
      id: assistantMessageId,
      type: 'assistant',
      content: '',
      isStreaming: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      // 使用流式问答
      cancelStreamRef.current = ragApiService.askQuestionStream(
        question,
        (data) => {
          // 处理流式消息
          if (data.type === 'content') {
            setMessages(prev => prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, content: data.content, isStreaming: !data.isComplete }
                : msg
            ));
          } else if (data.type === 'status') {
            // 可以显示状态信息
            console.log('状态:', data.message);
          } else if (data.type === 'sources') {
            // 可以显示来源信息
            setMessages(prev => prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, sources: data.data }
                : msg
            ));
          }
        },
        (error) => {
          console.error('流式问答错误:', error);
          setMessages(prev => prev.map(msg => 
            msg.id === assistantMessageId 
              ? { 
                  ...msg, 
                  content: '抱歉，我遇到了一些问题。请稍后再试或检查网络连接。', 
                  isStreaming: false,
                  isError: true
                }
              : msg
          ));
          setIsLoading(false);
        },
        () => {
          // 完成回调
          setIsLoading(false);
        }
      );

    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessageId 
          ? { 
              ...msg, 
              content: '抱歉，我遇到了一些问题。请稍后再试。', 
              isStreaming: false,
              isError: true
            }
          : msg
      ));
      setIsLoading(false);
    }
  };

  /**
   * 处理键盘事件
   */
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  /**
   * 选择建议问题
   */
  const selectSuggestion = (question) => {
    setInputValue(question);
    setShowSuggestions(false);
    sendMessage(question);
  };

  /**
   * 清空对话
   */
  const clearChat = () => {
    setMessages([]);
    setShowSuggestions(true);
    if (cancelStreamRef.current) {
      cancelStreamRef.current();
    }
  };

  return (
    <>
      {/* 浮动助手按钮 */}
      <motion.div
        className="ai-assistant-fab"
        onClick={toggleAssistant}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        animate={{
          rotate: isOpen ? 180 : 0,
        }}
      >
        <div className="fab-icon">
          {isOpen ? '✕' : '🤖'}
        </div>
        <div className="fab-glow"></div>
      </motion.div>

      {/* 助手面板 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="ai-assistant-panel"
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {/* 面板头部 */}
            <div className="assistant-header">
              <div className="header-content">
                <div className="assistant-avatar">🧠</div>
                <div className="header-text">
                  <h3>智能助手</h3>
                  <p className={`status ${status?.initialized ? 'online' : 'offline'}`}>
                    {status?.initialized ? '在线' : '离线'}
                  </p>
                </div>
              </div>
              <div className="header-actions">
                <button 
                  className="clear-btn"
                  onClick={clearChat}
                  title="清空对话"
                >
                  🗑️
                </button>
                <button 
                  className="close-btn"
                  onClick={toggleAssistant}
                  title="关闭"
                >
                  ✕
                </button>
              </div>
            </div>

            {/* 消息区域 */}
            <div className="messages-container">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  className={`message ${message.type}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="message-content">
                    {message.content}
                    {message.isStreaming && (
                      <span className="typing-indicator">▋</span>
                    )}
                  </div>
                  {message.sources && (
                    <div className="message-sources">
                      <small>参考: {message.sources.map(s => s.file_path).join(', ')}</small>
                    </div>
                  )}
                </motion.div>
              ))}
              
              {/* 建议问题 */}
              {showSuggestions && suggestions.length > 0 && (
                <div className="suggestions-container">
                  <h4>💡 您可以问我：</h4>
                  {suggestions.slice(0, 2).map((category, idx) => (
                    <div key={idx} className="suggestion-category">
                      <h5>{category.category}</h5>
                      {category.questions.slice(0, 2).map((question, qIdx) => (
                        <button
                          key={qIdx}
                          className="suggestion-btn"
                          onClick={() => selectSuggestion(question)}
                        >
                          {question}
                        </button>
                      ))}
                    </div>
                  ))}
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* 输入区域 */}
            <div className="input-container">
              <div className="input-wrapper">
                <textarea
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="输入您的问题..."
                  disabled={isLoading}
                  rows={1}
                />
                <button
                  className="send-btn"
                  onClick={() => sendMessage()}
                  disabled={!inputValue.trim() || isLoading}
                >
                  {isLoading ? '⏳' : '📤'}
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default AIAssistant;
