const { OpenAI } = require('openai');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs-extra');
const path = require('path');
const glob = require('glob');

// 简单的内存向量存储
class MemoryVectorStore {
  constructor() {
    this.documents = [];
    this.embeddings = [];
    this.metadatas = [];
  }

  async add(data) {
    const { ids, documents, embeddings, metadatas } = data;
    for (let i = 0; i < ids.length; i++) {
      this.documents.push({
        id: ids[i],
        content: documents[i],
        embedding: embeddings[i],
        metadata: metadatas[i]
      });
    }
  }

  async query(queryEmbedding, nResults = 5) {
    // 计算余弦相似度
    const similarities = this.documents.map(doc => {
      const similarity = this.cosineSimilarity(queryEmbedding, doc.embedding);
      return { ...doc, similarity };
    });

    // 按相似度排序并返回前 n 个结果
    const results = similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, nResults);

    return {
      documents: [results.map(r => r.content)],
      metadatas: [results.map(r => r.metadata)],
      distances: [results.map(r => 1 - r.similarity)] // 距离 = 1 - 相似度
    };
  }

  cosineSimilarity(a, b) {
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
    return dotProduct / (magnitudeA * magnitudeB);
  }

  getCount() {
    return this.documents.length;
  }
}

/**
 * RAG 智能助手服务
 * 实现检索增强生成功能，为 Industrial Geo Dev 项目提供智能问答
 */
class RAGService {
  constructor() {
    this.chroma = null;
    this.collection = null;
    this.openai = null;
    this.gemini = null;
    this.isInitialized = false;
    
    // 配置参数
    this.config = {
      collectionName: 'industrial_geo_knowledge',
      embeddingModel: 'text-embedding-3-small',
      chunkSize: 1000,
      chunkOverlap: 200,
      maxRetrievalResults: 5
    };
  }

  /**
   * 初始化 RAG 服务
   */
  async initialize() {
    try {
      console.log('🚀 初始化 RAG 智能助手服务...');

      // 初始化内存向量存储
      this.vectorStore = new MemoryVectorStore();

      // 初始化 OpenAI (用于嵌入)
      if (process.env.OPENAI_API_KEY) {
        this.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY
        });
      }

      // 初始化 Gemini (用于生成)
      if (process.env.GEMINI_API_KEY) {
        this.gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      }

      this.isInitialized = true;
      console.log('✅ RAG 服务初始化完成');

    } catch (error) {
      console.error('❌ RAG 服务初始化失败:', error);
      throw error;
    }
  }



  /**
   * 构建项目知识库
   */
  async buildKnowledgeBase() {
    try {
      console.log('🔨 开始构建项目知识库...');
      
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // 收集项目文档和代码
      const documents = await this.collectProjectDocuments();
      
      // 处理并存储文档
      let processedCount = 0;
      for (const doc of documents) {
        await this.processAndStoreDocument(doc);
        processedCount++;
        
        if (processedCount % 10 === 0) {
          console.log(`📄 已处理 ${processedCount}/${documents.length} 个文档`);
        }
      }
      
      console.log(`✅ 知识库构建完成，共处理 ${processedCount} 个文档`);
      
    } catch (error) {
      console.error('❌ 构建知识库失败:', error);
      throw error;
    }
  }

  /**
   * 收集项目文档和代码文件
   */
  async collectProjectDocuments() {
    const projectRoot = path.resolve(__dirname, '../..');
    const documents = [];
    
    // 文档文件模式
    const documentPatterns = [
      '*.md',
      'src/**/*.jsx',
      'src/**/*.js',
      'src/**/*.css',
      'backend-server/**/*.js',
      'backend-server/**/*.cjs',
      'backend-server/**/*.py',
      'package.json',
      'backend-server/package.json'
    ];
    
    // 排除模式
    const excludePatterns = [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.git/**',
      '**/chroma_db/**'
    ];
    
    for (const pattern of documentPatterns) {
      const files = glob.sync(pattern, {
        cwd: projectRoot,
        ignore: excludePatterns,
        absolute: true
      });
      
      for (const filePath of files) {
        try {
          const content = await fs.readFile(filePath, 'utf-8');
          const relativePath = path.relative(projectRoot, filePath);
          
          documents.push({
            path: relativePath,
            fullPath: filePath,
            content: content,
            type: this.getDocumentType(filePath),
            size: content.length
          });
        } catch (error) {
          console.warn(`⚠️ 无法读取文件 ${filePath}:`, error.message);
        }
      }
    }
    
    console.log(`📁 收集到 ${documents.length} 个文档文件`);
    return documents;
  }

  /**
   * 获取文档类型
   */
  getDocumentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath).toLowerCase();
    
    if (ext === '.md') return 'documentation';
    if (ext === '.jsx' || ext === '.js') return 'frontend_code';
    if (ext === '.cjs') return 'backend_code';
    if (ext === '.py') return 'python_code';
    if (ext === '.css') return 'styles';
    if (fileName === 'package.json') return 'configuration';
    
    return 'other';
  }

  /**
   * 处理并存储单个文档
   */
  async processAndStoreDocument(document) {
    try {
      // 将文档切分成块
      const chunks = this.chunkDocument(document);

      // 为每个块生成嵌入向量并存储
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const chunkId = `${document.path}_chunk_${i}`;

        // 生成嵌入向量
        const embedding = await this.generateEmbedding(chunk.content);

        // 存储到内存向量数据库
        await this.vectorStore.add({
          ids: [chunkId],
          documents: [chunk.content],
          embeddings: [embedding],
          metadatas: [{
            file_path: document.path,
            file_type: document.type,
            chunk_index: i,
            chunk_size: chunk.content.length,
            created_at: new Date().toISOString()
          }]
        });
      }

    } catch (error) {
      console.error(`❌ 处理文档失败 ${document.path}:`, error);
    }
  }

  /**
   * 将文档切分成块
   */
  chunkDocument(document) {
    const content = document.content;
    const chunks = [];
    
    // 根据文档类型选择不同的切分策略
    if (document.type === 'documentation') {
      // Markdown 文档按章节切分
      return this.chunkMarkdown(content);
    } else if (document.type.includes('code')) {
      // 代码文件按函数/类切分
      return this.chunkCode(content, document.type);
    } else {
      // 其他文件按固定大小切分
      return this.chunkBySize(content);
    }
  }

  /**
   * 按章节切分 Markdown 文档
   */
  chunkMarkdown(content) {
    const chunks = [];
    const sections = content.split(/\n(?=#{1,6}\s)/);
    
    for (const section of sections) {
      if (section.trim().length > 0) {
        if (section.length <= this.config.chunkSize) {
          chunks.push({
            content: section.trim(),
            type: 'section'
          });
        } else {
          // 大章节进一步切分
          const subChunks = this.chunkBySize(section);
          chunks.push(...subChunks);
        }
      }
    }
    
    return chunks;
  }

  /**
   * 按函数/类切分代码
   */
  chunkCode(content, type) {
    const chunks = [];
    
    if (type === 'frontend_code' || type === 'backend_code') {
      // JavaScript/JSX 代码按函数切分
      const functionPattern = /(?:function\s+\w+|const\s+\w+\s*=|class\s+\w+|export\s+(?:default\s+)?(?:function|class|const))/g;
      const matches = [...content.matchAll(functionPattern)];
      
      if (matches.length > 0) {
        let lastIndex = 0;
        for (let i = 0; i < matches.length; i++) {
          const match = matches[i];
          const nextMatch = matches[i + 1];
          const endIndex = nextMatch ? nextMatch.index : content.length;
          
          const chunk = content.slice(lastIndex, endIndex).trim();
          if (chunk.length > 0) {
            chunks.push({
              content: chunk,
              type: 'function'
            });
          }
          lastIndex = match.index;
        }
      }
    }
    
    // 如果没有找到函数，按固定大小切分
    if (chunks.length === 0) {
      return this.chunkBySize(content);
    }
    
    return chunks;
  }

  /**
   * 按固定大小切分文档
   */
  chunkBySize(content) {
    const chunks = [];
    const lines = content.split('\n');
    let currentChunk = '';
    
    for (const line of lines) {
      if (currentChunk.length + line.length > this.config.chunkSize) {
        if (currentChunk.trim().length > 0) {
          chunks.push({
            content: currentChunk.trim(),
            type: 'text'
          });
        }
        currentChunk = line;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + line;
      }
    }
    
    if (currentChunk.trim().length > 0) {
      chunks.push({
        content: currentChunk.trim(),
        type: 'text'
      });
    }
    
    return chunks;
  }

  /**
   * 生成文本嵌入向量
   */
  async generateEmbedding(text) {
    try {
      if (!this.openai) {
        throw new Error('OpenAI 客户端未初始化');
      }
      
      const response = await this.openai.embeddings.create({
        model: this.config.embeddingModel,
        input: text
      });
      
      return response.data[0].embedding;
      
    } catch (error) {
      console.error('❌ 生成嵌入向量失败:', error);
      throw error;
    }
  }

  /**
   * 智能问答
   */
  async askQuestion(question, userId = null) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      console.log(`🤔 用户提问: ${question}`);
      
      // 1. 检索相关知识片段
      const relevantChunks = await this.retrieveRelevantChunks(question);
      
      // 2. 构建上下文提示
      const prompt = this.buildPrompt(question, relevantChunks);
      
      // 3. 调用 Gemini 生成回答
      const answer = await this.generateAnswer(prompt);
      
      // 4. 记录问答历史（可选）
      await this.logQA(question, answer, userId);
      
      return {
        question,
        answer,
        sources: relevantChunks.map(chunk => ({
          file_path: chunk.metadata.file_path,
          file_type: chunk.metadata.file_type
        })),
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ 智能问答失败:', error);
      throw error;
    }
  }

  /**
   * 检索相关知识片段
   */
  async retrieveRelevantChunks(question) {
    try {
      // 检查向量存储是否已初始化且有数据
      if (!this.vectorStore || this.vectorStore.documents.length === 0) {
        console.log('⚠️ 知识库为空，返回默认回答');
        return [];
      }

      // 生成问题的嵌入向量
      const questionEmbedding = await this.generateEmbedding(question);

      // 在内存向量数据库中搜索相似内容
      const results = await this.vectorStore.query(
        questionEmbedding,
        this.config.maxRetrievalResults
      );

      // 格式化结果
      const chunks = [];
      for (let i = 0; i < results.documents[0].length; i++) {
        chunks.push({
          content: results.documents[0][i],
          metadata: results.metadatas[0][i],
          distance: results.distances[0][i]
        });
      }

      console.log(`🔍 检索到 ${chunks.length} 个相关知识片段`);
      return chunks;

    } catch (error) {
      console.error('❌ 检索知识片段失败:', error);
      throw error;
    }
  }

  /**
   * 构建提示词
   */
  buildPrompt(question, relevantChunks) {
    const context = relevantChunks
      .map(chunk => `文件: ${chunk.metadata.file_path}\n内容: ${chunk.content}`)
      .join('\n\n---\n\n');
    
    return `你是 Industrial Geo Dev 项目的智能助手，专门帮助用户理解和使用这个工业地理开发系统。

项目背景：
Industrial Geo Dev 是一个综合性的工业地理开发平台，包含地图可视化、数据分析、站点选择、劳动力分析等功能。系统使用 React + Node.js 技术栈，集成了多种地理信息和经济数据 API。

相关知识片段：
${context}

用户问题：${question}

请基于上述项目知识，为用户提供准确、详细、实用的回答。回答应该：
1. 直接回答用户的问题
2. 提供具体的操作步骤（如果适用）
3. 引用相关的文件或功能模块
4. 使用友好、专业的语调
5. 如果涉及代码，提供简洁的代码示例

回答：`;
  }

  /**
   * 生成回答
   */
  async generateAnswer(prompt) {
    try {
      if (!this.gemini) {
        throw new Error('Gemini 客户端未初始化');
      }
      
      const model = this.gemini.getGenerativeModel({ 
        model: 'gemini-2.5-flash-lite' 
      });
      
      const result = await model.generateContent(prompt);
      const response = await result.response;
      
      return response.text();
      
    } catch (error) {
      console.error('❌ 生成回答失败:', error);
      throw error;
    }
  }

  /**
   * 记录问答历史
   */
  async logQA(question, answer, userId) {
    try {
      // 这里可以将问答记录存储到数据库
      // 暂时只记录到控制台
      console.log(`📝 问答记录 - 用户: ${userId || 'anonymous'}, 问题: ${question.substring(0, 50)}...`);
    } catch (error) {
      console.error('❌ 记录问答历史失败:', error);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      hasOpenAI: !!this.openai,
      hasGemini: !!this.gemini,
      hasCollection: !!this.collection,
      config: this.config
    };
  }
}

module.exports = RAGService;
